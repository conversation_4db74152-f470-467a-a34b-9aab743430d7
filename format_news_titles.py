#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻标题格式化处理脚本
根据不同热点源处理格式，只保留标题
"""

import json
import sys
from datetime import datetime
from pathlib import Path


class NewsFormatter:
    """新闻数据格式化器"""
    
    def __init__(self):
        self.source_names = {
            'weibo': '🔸 微博热搜',
            'baidu': '🔸 百度热搜', 
            'zhihu': '🔸 知乎热榜',
            'toutiao': '🔸 今日头条',
            'bilibili': '🔸 B站热搜',
            'github': '🔸 GitHub趋势',
            'v2ex': '🔸 V2EX热门',
            'ithome': '🔸 IT之家'
        }
    
    def load_news_data(self, file_path):
        """加载新闻数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"❌ 文件不存在: {file_path}")
            return None
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析错误: {e}")
            return None
        except Exception as e:
            print(f"❌ 读取文件错误: {e}")
            return None
    
    def extract_titles(self, data):
        """提取各个热点源的标题"""
        formatted_data = {}
        
        for source, items in data.items():
            if not items:  # 跳过空数据源
                continue
                
            titles = []
            for item in items:
                if isinstance(item, dict) and 'title' in item:
                    title = item['title'].strip()
                    if title:  # 确保标题不为空
                        titles.append(title)
            
            if titles:  # 只保存有标题的数据源
                formatted_data[source] = titles
        
        return formatted_data
    
    def format_output_text(self, formatted_data):
        """格式化输出文本"""
        output_lines = []
        output_lines.append("📋 热点新闻标题汇总")
        output_lines.append("=" * 50)
        output_lines.append(f"⏰ 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        output_lines.append("")
        
        total_count = 0
        for source, titles in formatted_data.items():
            source_name = self.source_names.get(source, f'🔸 {source.upper()}')
            output_lines.append(f"{source_name} ({len(titles)}条):")
            output_lines.append("-" * 40)
            
            for i, title in enumerate(titles, 1):
                output_lines.append(f"  {i:2d}. {title}")
            
            output_lines.append("")
            total_count += len(titles)
        
        output_lines.append("=" * 50)
        output_lines.append(f"📊 总计: {total_count} 条新闻标题")
        
        return "\n".join(output_lines)
    
    def save_formatted_data(self, formatted_data, output_file):
        """保存格式化后的数据"""
        # 保存为JSON格式
        json_file = output_file.with_suffix('.json')
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(formatted_data, f, ensure_ascii=False, indent=2)
        
        # 保存为文本格式
        txt_file = output_file.with_suffix('.txt')
        text_content = self.format_output_text(formatted_data)
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write(text_content)
        
        return json_file, txt_file
    
    def process_file(self, input_file, output_prefix=None):
        """处理单个文件"""
        input_path = Path(input_file)
        
        if not input_path.exists():
            print(f"❌ 输入文件不存在: {input_file}")
            return False
        
        # 加载数据
        print(f"📖 正在读取文件: {input_file}")
        data = self.load_news_data(input_path)
        if not data:
            return False
        
        # 提取标题
        print("🔄 正在提取标题...")
        formatted_data = self.extract_titles(data)
        
        if not formatted_data:
            print("❌ 没有找到有效的标题数据")
            return False
        
        # 生成输出文件名
        if output_prefix:
            output_file = Path(output_prefix)
        else:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = input_path.parent / f"formatted_titles_{timestamp}"
        
        # 保存结果
        print("💾 正在保存格式化结果...")
        json_file, txt_file = self.save_formatted_data(formatted_data, output_file)
        
        # 显示统计信息
        print("\n" + "=" * 50)
        print("✅ 处理完成!")
        print("=" * 50)
        
        total_count = sum(len(titles) for titles in formatted_data.values())
        print(f"📊 数据统计:")
        print(f"   - 总标题数: {total_count}")
        print(f"   - 数据源数: {len(formatted_data)}")
        
        print(f"\n📁 输出文件:")
        print(f"   - JSON格式: {json_file}")
        print(f"   - 文本格式: {txt_file}")
        
        # 显示各数据源统计
        print(f"\n📋 各数据源统计:")
        for source, titles in formatted_data.items():
            source_name = self.source_names.get(source, source.upper())
            print(f"   - {source_name}: {len(titles)} 条")
        
        return True


def main():
    """主函数"""
    formatter = NewsFormatter()
    
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("📝 使用方法:")
        print(f"   python {sys.argv[0]} <输入文件> [输出前缀]")
        print("\n📖 示例:")
        print(f"   python {sys.argv[0]} news_data_20250707_115444.json")
        print(f"   python {sys.argv[0]} news_data_20250707_115444.json my_titles")
        return
    
    input_file = sys.argv[1]
    output_prefix = sys.argv[2] if len(sys.argv) > 2 else None
    
    print("🎯 新闻标题格式化工具")
    print("=" * 50)
    
    success = formatter.process_file(input_file, output_prefix)
    
    if success:
        print("\n✨ 程序执行完成!")
    else:
        print("\n❌ 程序执行失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()
