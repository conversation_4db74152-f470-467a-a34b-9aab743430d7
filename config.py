"""
配置文件加载模块
从.env文件中加载API密钥和其他配置
"""

import os
from typing import Dict, Any


def load_env_file(env_path: str = ".env") -> Dict[str, str]:
    """加载.env文件"""
    env_vars = {}
    
    if not os.path.exists(env_path):
        print(f"⚠️ 未找到配置文件: {env_path}")
        print("请创建.env文件并配置相关密钥")
        return env_vars
    
    try:
        with open(env_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                # 跳过注释和空行
                if line and not line.startswith('#'):
                    if '=' in line:
                        key, value = line.split('=', 1)
                        env_vars[key.strip()] = value.strip()
        
        print(f"✅ 成功加载配置文件: {env_path}")
        return env_vars
        
    except Exception as e:
        print(f"❌ 加载配置文件失败: {e}")
        return env_vars


def get_llm_config() -> Dict[str, Any]:
    """获取LLM API配置（支持多API key）"""
    env_vars = load_env_file()

    # 收集所有API key
    api_keys = []
    for i in range(1, 10):  # 支持最多9个API key
        key = env_vars.get(f"LLM_API_KEY_{i}", "")
        if key:
            api_keys.append(key)

    # 如果没有找到编号的key，尝试单个key
    if not api_keys:
        single_key = env_vars.get("LLM_API_KEY", "")
        if single_key:
            api_keys.append(single_key)

    config = {
        "api_keys": api_keys,  # 多个API key列表
        "base_url": "https://www.sophnet.com/api/open-apis/chat/completions",
        "model": "DeepSeek-V3-Fast"
    }

    # 检查必要的配置
    if not api_keys:
        print("⚠️ LLM配置缺少: api_keys")
        print("请在.env文件中配置LLM_API_KEY_1, LLM_API_KEY_2等")
    else:
        print(f"✅ 加载了 {len(api_keys)} 个LLM API Key，支持并发处理")

    return config


def get_embedding_config() -> Dict[str, Any]:
    """获取Embedding API配置"""
    env_vars = load_env_file()
    
    config = {
        "api_key": env_vars.get("EMBEDDING_API_KEY", ""),
        "base_url": "https://www.sophnet.com/api/open-apis/projects/{project_id}/easyllms/embeddings",
        "project_id": env_vars.get("EMBEDDING_PROJECT_ID", ""),
        "easyllm_id": env_vars.get("EMBEDDING_EASYLLM_ID", ""),
        "dimensions": 768,
        "rate_limit": 60
    }
    
    # 检查必要的配置
    missing_keys = [key for key, value in config.items() 
                   if not value and key not in ["dimensions", "rate_limit", "base_url"]]
    if missing_keys:
        print(f"⚠️ Embedding配置缺少: {missing_keys}")
        print("请在.env文件中配置相关密钥")
    
    return config


def get_email_config() -> Dict[str, Any]:
    """获取邮件配置"""
    env_vars = load_env_file()

    config = {
        "from_addr": env_vars.get("EMAIL_FROM_ADDR", ""),
        "password": env_vars.get("EMAIL_PASSWORD", ""),
        "to_addr": env_vars.get("EMAIL_TO_ADDR", ""),
        "smtp_server": env_vars.get("EMAIL_SMTP_SERVER", "smtp.qq.com"),
        "smtp_port": int(env_vars.get("EMAIL_SMTP_PORT", "465"))
    }

    return config


def get_database_config() -> Dict[str, Any]:
    """获取数据库配置"""
    env_vars = load_env_file()

    config = {
        "host": env_vars.get("DB_HOST", ""),
        "port": int(env_vars.get("DB_PORT", "3306")),
        "user": env_vars.get("DB_USER", ""),
        "password": env_vars.get("DB_PASSWORD", ""),
        "database": env_vars.get("DB_NAME", ""),
        "ssl_mode": env_vars.get("DB_SSL_MODE", "REQUIRED"),
        "charset": "utf8mb4"
    }

    # 检查必要的配置
    required_keys = ["host", "user", "password", "database"]
    missing_keys = [key for key in required_keys if not config[key]]
    if missing_keys:
        print(f"⚠️ 数据库配置缺少: {missing_keys}")
        print("请在.env文件中配置相关数据库连接信息")
    else:
        print(f"✅ 数据库配置加载成功: {config['user']}@{config['host']}:{config['port']}/{config['database']}")

    return config


def print_config_status():
    """打印配置状态"""
    print("\n📋 配置状态检查:")
    print("-" * 40)

    # 检查LLM配置
    llm_config = get_llm_config()
    llm_ok = bool(llm_config.get("api_keys", []))
    api_key_count = len(llm_config.get("api_keys", []))
    print(f"🤖 LLM API: {'✅ 已配置' if llm_ok else '❌ 未配置'} ({api_key_count} 个key)")

    # 检查Embedding配置
    embedding_config = get_embedding_config()
    embedding_ok = all(embedding_config[key] for key in ["api_key", "project_id", "easyllm_id"])
    print(f"🔤 Embedding API: {'✅ 已配置' if embedding_ok else '❌ 未配置'}")

    # 检查数据库配置
    database_config = get_database_config()
    database_ok = all(database_config[key] for key in ["host", "user", "password", "database"])
    print(f"🗄️ 数据库配置: {'✅ 已配置' if database_ok else '❌ 未配置'}")

    # 检查邮件配置（现在是可选的）
    email_config = get_email_config()
    email_ok = all(email_config[key] for key in ["from_addr", "password", "to_addr"])
    print(f"📧 邮件配置: {'✅ 已配置' if email_ok else '❌ 未配置'} (可选)")

    print("-" * 40)

    if not (llm_ok and embedding_ok and database_ok):
        print("⚠️ 请在.env文件中配置缺少的API密钥和数据库连接信息")
        return False

    return True


if __name__ == "__main__":
    print_config_status()
