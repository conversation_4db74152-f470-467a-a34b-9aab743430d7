# 🎯 热点新闻标题爬取整合工具

一个基于 Python 的多平台热点新闻爬虫工具，支持自动抓取和格式化各大平台的热门内容。

## ✨ 功能特色

- 🚀 **多平台支持**: 支持微博、百度、知乎、今日头条、B站、GitHub、V2EX、IT之家等8个主流平台
- 📊 **智能格式化**: 自动提取标题并生成格式化报告
- 📁 **文件管理**: 所有输出文件自动保存到 `news_output` 文件夹
- 🔧 **灵活配置**: 支持代理设置、平台开关、数量限制等配置
- 📝 **多种输出**: 支持 JSON 和 TXT 两种格式输出
- 🎨 **美观界面**: 带有 emoji 和颜色的友好命令行界面

## 📦 安装依赖

```bash
pip install -r requirements.txt
```

### 依赖包说明
- `requests`: HTTP 请求库
- `beautifulsoup4`: HTML 解析库
- `feedparser`: RSS/Atom 解析库

## 🚀 快速开始

### 1. 基本使用（推荐）

直接运行爬虫，自动抓取所有平台数据并格式化：

```bash
python news_crawler.py
```

程序会：
1. 自动抓取所有启用平台的热点数据
2. 保存原始数据到 `news_output/news_data_YYYYMMDD_HHMMSS.json`
3. 自动格式化标题并保存到 `news_output/formatted_titles_YYYYMMDD_HHMMSS.json` 和 `.txt`

### 2. 仅格式化已有数据

如果你已有数据文件，只想格式化标题：

```bash
python news_crawler.py --format-only <数据文件> [输出前缀]
```

示例：
```bash
python news_crawler.py --format-only news_data_20250707_115444.json my_titles
```

## 📊 支持的平台

| 平台 | 状态 | 数据类型 | 默认数量 |
|------|------|----------|----------|
| 🔸 微博热搜 | ✅ | 热搜话题 | 50 |
| 🔸 百度热搜 | ✅ | 热搜关键词 | 50 |
| 🔸 知乎热榜 | ✅ | 热门问题 | 50 |
| 🔸 今日头条 | ✅ | 热门新闻 | 50 |
| 🔸 B站热搜 | ✅ | 热搜关键词 | 50 |
| 🔸 GitHub趋势 | ⚠️ | 趋势项目 | 20 |
| 🔸 V2EX热门 | ✅ | 热门话题 | 50 |
| 🔸 IT之家 | ⚠️ | 科技新闻 | 20 |

> ⚠️ 注：GitHub 和 IT之家 可能因网络或API限制偶尔获取失败

## 📁 输出文件说明

所有文件都保存在 `news_output/` 文件夹中：

### 原始数据文件
- **文件名**: `news_data_YYYYMMDD_HHMMSS.json`
- **格式**: JSON
- **内容**: 包含完整的原始数据，包括标题、链接、额外信息等

### 格式化标题文件
- **JSON文件**: `formatted_titles_YYYYMMDD_HHMMSS.json`
  - 纯标题数据，按平台分类
  - 便于程序进一步处理
  
- **文本文件**: `formatted_titles_YYYYMMDD_HHMMSS.txt`
  - 人类友好的格式化报告
  - 包含统计信息和时间戳
  - 适合直接阅读和分享

## ⚙️ 配置选项

### 创建配置文件 `crawler_config.py`

```python
# 平台数量限制
PLATFORM_LIMITS = {
    'zhihu': 50,        # 知乎热榜数量
    'bilibili': 50,     # B站热搜数量
    'github': 25,       # GitHub趋势数量
    'v2ex': 50,         # V2EX热门数量
    'ithome': 30        # IT之家新闻数量
}

# 网络配置
NETWORK_CONFIG = {
    'timeout': 15,          # 请求超时时间（秒）
    'max_retries': 3,       # 最大重试次数
    'retry_delay': 2,       # 重试延迟（秒）
    'request_interval': 1   # 请求间隔（秒）
}

# 代理配置
PROXY_CONFIG = {
    'enabled': False,                    # 是否启用代理
    'http_proxy': 'http://127.0.0.1:7890',   # HTTP代理
    'https_proxy': 'http://127.0.0.1:7890'   # HTTPS代理
}

# 输出配置
OUTPUT_CONFIG = {
    'save_to_file': True,           # 是否保存到文件
    'show_summary': True,           # 是否显示摘要
    'summary_preview_count': 5      # 摘要预览条数
}

# 平台开关
PLATFORM_ENABLED = {
    'weibo': True,      # 微博热搜
    'baidu': True,      # 百度热搜
    'zhihu': True,      # 知乎热榜
    'toutiao': True,    # 今日头条
    'bilibili': True,   # B站热搜
    'github': True,     # GitHub趋势
    'v2ex': True,       # V2EX热门
    'ithome': True      # IT之家
}
```

## 🔧 高级用法

### 使用代理

```python
from news_crawler import NewsCrawler

# 创建带代理的爬虫实例
crawler = NewsCrawler(proxy="http://127.0.0.1:7890")
news_data = crawler.get_all_news()
```

### 单独获取某个平台

```python
from news_crawler import NewsCrawler

crawler = NewsCrawler()

# 只获取知乎热榜
zhihu_data = crawler.zhihu_hot_list()
print(f"获取到 {len(zhihu_data)} 条知乎数据")

# 只获取微博热搜
weibo_data = crawler.weibo_hot_search()
print(f"获取到 {len(weibo_data)} 条微博数据")
```

### 自定义格式化

```python
from news_crawler import NewsFormatter
import json

# 加载数据
with open('news_output/news_data_20250707_115444.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# 格式化
formatter = NewsFormatter()
json_file, txt_file = formatter.format_news_data(data, "custom_output")
```

## 📋 输出示例

### 控制台输出
```
🎯 新闻爬虫启动
基于newsnow项目的Python实现
==================================================
🚀 开始抓取新闻数据...
==================================================
✅ 微博热搜: 获取到 50 条数据
✅ 百度热搜: 获取到 50 条数据
✅ 知乎热榜: 获取到 50 条数据
...
📊 抓取完成!
总计获取 300 条新闻数据
💾 数据已保存到: news_output/news_data_20250707_120309.json

🔄 自动开始格式化标题...
💾 正在保存格式化结果...
✅ 标题格式化完成!
📁 格式化文件:
   - JSON格式: news_output/formatted_titles_20250707_120314.json
   - 文本格式: news_output/formatted_titles_20250707_120314.txt
```

### 格式化文本输出示例
```
📋 热点新闻标题汇总
==================================================
⏰ 生成时间: 2025-07-07 12:03:14

🔸 微博热搜 (50条):
----------------------------------------
   1. 果然高铁F座最受欢迎
   2. 实付2元点外卖被偷气得直接报警
   3. 七七事变背后的历史真相
   ...

🔸 百度热搜 (50条):
----------------------------------------
   1. 央视曝光零差评背后猫腻
   2. 王毅这句话的含金量还在飙升
   ...
```

## ❓ 常见问题

### Q: 为什么某些平台获取到0条数据？
A: 可能的原因：
- 网络连接问题
- 平台API发生变化
- 需要使用代理访问
- 平台临时限制访问

### Q: 如何提高获取成功率？
A: 建议：
- 使用稳定的网络环境
- 配置合适的代理
- 增加重试次数和超时时间
- 适当增加请求间隔

### Q: 数据格式是什么？
A: 统一的JSON格式，每条新闻包含：
- `id`: 唯一标识
- `title`: 标题
- `url`: 链接地址
- `extra`: 额外信息（图标、热度等）

### Q: 如何定制输出格式？
A: 可以修改 `NewsFormatter` 类中的 `format_output_text` 方法来自定义输出格式。

## 🛠️ 开发说明

### 项目结构
```
热点新闻标题爬取整合/
├── news_crawler.py          # 主程序文件
├── crawler_config.py        # 配置文件（可选）
├── requirements.txt         # 依赖包列表
├── README.md               # 说明文档
├── news_output/            # 输出文件夹
│   ├── news_data_*.json    # 原始数据文件
│   ├── formatted_titles_*.json  # 格式化JSON文件
│   └── formatted_titles_*.txt   # 格式化文本文件
└── 使用说明.md             # 详细使用说明
```

### 添加新平台
1. 在 `NewsCrawler` 类中添加新的方法
2. 在 `get_all_news` 方法中注册新平台
3. 在配置文件中添加相应配置

## 📄 许可证

本项目基于 MIT 许可证开源。

## 🎯 使用场景

### 1. 媒体监控
- 实时监控各平台热点话题
- 分析舆情趋势和热点变化
- 为新闻编辑提供素材参考

### 2. 数据分析
- 收集大量标题数据用于NLP分析
- 研究不同平台的内容特点
- 进行热点话题的统计分析

### 3. 内容创作
- 获取当前热门话题灵感
- 了解各平台用户关注点
- 为内容创作提供方向参考

### 4. 竞品分析
- 监控竞争对手在各平台的表现
- 分析行业热点和趋势
- 制定内容策略

## 🔍 技术实现

### 数据获取方式
- **微博**: 通过官方API获取热搜数据
- **百度**: 解析热搜页面HTML内容
- **知乎**: 使用知乎热榜API
- **今日头条**: 调用热榜接口
- **B站**: 使用搜索热词API
- **GitHub**: 通过GitHub API获取趋势项目
- **V2EX**: 解析RSS订阅源
- **IT之家**: 爬取首页新闻列表

### 错误处理机制
- 自动重试失败的请求
- 跳过无法访问的平台
- 详细的错误日志记录
- 优雅的异常处理

### 性能优化
- 使用Session复用连接
- 合理的请求间隔避免被限制
- 异步处理多个平台数据
- 内存友好的数据处理

## 📈 更新日志

### v2.0.0 (2025-07-07)
- ✨ 新增自动格式化功能
- 📁 所有输出文件自动保存到专用文件夹
- 🎨 优化用户界面和输出格式
- 🔧 支持仅格式化模式
- 📊 增强统计信息显示

### v1.0.0
- 🚀 基础爬虫功能
- 📊 支持8个主流平台
- ⚙️ 灵活的配置系统
- 🔄 自动重试机制

## 🚨 注意事项

1. **合规使用**: 请遵守各平台的robots.txt和使用条款
2. **频率控制**: 避免过于频繁的请求，建议间隔1秒以上
3. **网络环境**: 某些平台可能需要特定的网络环境或代理
4. **数据时效**: 热点数据具有时效性，建议定期更新
5. **存储空间**: 长期运行请注意清理旧的数据文件

## 🔮 未来计划

- [ ] 支持更多平台（抖音、小红书等）
- [ ] 添加数据可视化功能
- [ ] 实现定时任务和自动化部署
- [ ] 增加数据去重和相似度分析
- [ ] 支持关键词过滤和分类
- [ ] 添加Web界面管理
- [ ] 集成数据库存储
- [ ] 支持多语言国际化

## 🤝 贡献指南

### 如何贡献
1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 代码规范
- 使用 Python 3.7+ 语法
- 遵循 PEP 8 代码风格
- 添加适当的注释和文档字符串
- 确保代码通过基本测试

### 报告问题
- 使用 GitHub Issues 报告 Bug
- 提供详细的错误信息和复现步骤
- 包含系统环境和Python版本信息

## 📞 联系方式

- 📧 Email: 通过 GitHub Issues 联系
- 💬 讨论: GitHub Discussions
- 🐛 Bug报告: GitHub Issues

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢以下开源项目的支持：
- [requests](https://github.com/psf/requests) - HTTP库
- [BeautifulSoup](https://www.crummy.com/software/BeautifulSoup/) - HTML解析
- [feedparser](https://github.com/kurtmckee/feedparser) - RSS解析

---

⭐ **如果这个项目对你有帮助，请给个 Star 支持一下！**

🔔 **Watch 本项目以获取最新更新通知**
